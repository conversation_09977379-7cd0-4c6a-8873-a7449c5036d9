# 用户问题分析实验方案目录

本目录存放不同的用户问题分析穿刺方案脚本，用于对比分析不同技术路线的效果。实验涵盖整个分析流程：数据预处理、向量化、特征增强、聚类/分类、结果评估等。

## 命名规范

脚本命名格式：`{核心技术栈}_{主要方法}_{版本/特征}.py`

### 示例：
- `jina_embeddings_kmeans_baseline.py` - 基线方案：Jina嵌入 + KMeans聚类
- `bge_embeddings_dbscan_optimized.py` - BGE嵌入 + DBSCAN + 优化
- `openai_embeddings_hierarchical_pca.py` - OpenAI嵌入 + 层次聚类 + PCA降维
- `bert_finetuned_classification.py` - 微调BERT + 分类方法
- `multi_stage_ensemble.py` - 多阶段集成方案
- `keyword_tfidf_traditional.py` - 传统关键词 + TF-IDF方法

## 技术维度分类

### 1. 向量化/嵌入技术
- **预训练嵌入模型**：jina-embeddings, bge-large-zh, OpenAI embeddings
- **传统特征工程**：TF-IDF, Word2Vec, FastText
- **深度学习**：BERT, RoBERTa, T5微调
- **多模态**：文本+代码联合嵌入

### 2. 特征增强技术
- **降维**：PCA, UMAP, t-SNE
- **特征选择**：方差筛选, 相关性分析, LASSO
- **数据增强**：同义词替换, 回译, 生成式增强
- **领域适应**：领域词典, 专业术语权重

### 3. 分析方法
- **无监督聚类**：KMeans, DBSCAN, 层次聚类, GMM
- **主题建模**：LDA, BERTopic, Top2Vec
- **监督学习**：分类器训练, 少样本学习
- **半监督**：伪标签, 一致性正则化

### 4. 评估策略
- **内部指标**：轮廓系数, Calinski-Harabasz指数
- **外部指标**：NMI, ARI, V-measure（与人工标签对比）
- **任务导向**：问题解决效率, 用户满意度

## 现有实验方案

### 1. jina_embeddings_kmeans_baseline.py
- **技术栈**：jina-embeddings-v2-base-zh + KMeans
- **特点**：基础baseline版本，简单直接

### 2. jina_embeddings_kmeans_v2.py
- **技术栈**：jina-embeddings-v2-base-zh + KMeans + 详细分析
- **特点**：增强版，包含完整的结果输出和分析报告

### 3. jina_embeddings_bertopic_advanced.py
- **技术栈**：jina-embeddings-v2-base-zh + BERTopic (UMAP + HDBSCAN)
- **特点**：主题建模方案，自动发现主题数量，提供主题关键词和代表性文档，可解释性强

## 实验流程

### 阶段1：数据预处理
- 文本清洗和标准化
- 代码片段处理
- 特殊字符和格式处理

### 阶段2：向量化/特征提取
- 选择合适的嵌入模型
- 处理长文本截断问题
- 考虑中英文混合场景

### 阶段3：特征增强（可选）
- 降维处理
- 特征融合
- 领域知识注入

### 阶段4：分析建模
- 聚类/分类算法选择
- 超参数调优
- 模型集成

### 阶段5：结果评估
- 多维度评估指标
- 可视化分析
- 与人工标签对比

## 运行指南

1. **运行单个实验**：
   ```bash
   cd user_analysis_experiments
   python jina_embeddings_kmeans_v2.py
   ```

2. **批量实验对比**：
   ```bash
   # 运行多个方案
   python jina_embeddings_kmeans_v2.py
   python bge_embeddings_dbscan.py
   python bert_finetuned_classification.py
   
   # 对比结果
   python compare_experiments.py
   ```

3. **查看结果**：
   - 详细结果CSV：`../results/analysis_results_{方案名}_{时间戳}.csv`
   - 汇总报告：`../results/analysis_summary_{方案名}_{时间戳}.txt`
   - 可视化图表：`../results/visualization_{方案名}_{时间戳}.png`

## 实验建议

### 快速验证方案
1. **传统方案**：TF-IDF + KMeans（快速基线）
2. **预训练方案**：jina-embeddings + KMeans（平衡效果与效率）
3. **深度学习方案**：BERT微调 + 分类（追求最佳效果）

### 进阶优化方案
1. **多阶段流水线**：预聚类 + 细分类 + 后处理
2. **集成学习**：多模型投票 + 置信度融合
3. **主动学习**：不确定性采样 + 人工标注 + 迭代优化

## 文件结构

```
user_analysis_experiments/
├── README.md                              # 本说明文档
├── jina_embeddings_kmeans_baseline.py     # 基线方案
├── jina_embeddings_kmeans_v2.py           # 增强版
├── bge_embeddings_dbscan.py              # BGE + DBSCAN方案
├── bert_finetuned_classification.py      # BERT微调方案
├── traditional_tfidf_kmeans.py           # 传统TF-IDF方案
├── multi_stage_pipeline.py               # 多阶段流水线
├── ensemble_methods.py                   # 集成学习方案
├── utils/                                 # 工具函数
│   ├── data_preprocessing.py             # 数据预处理
│   ├── feature_engineering.py           # 特征工程
│   ├── evaluation_metrics.py            # 评估指标
│   └── visualization.py                 # 可视化工具
└── compare_experiments.py               # 实验对比脚本

../results/                               # 结果输出目录
├── analysis_results_*.csv               # 详细分析结果
├── analysis_summary_*.txt               # 汇总分析报告
└── visualization_*.png                  # 可视化图表
```

## 后续扩展方向

1. **多语言支持**：处理中英文混合、代码语言识别
2. **实时分析**：流式处理、增量学习
3. **个性化**：用户画像、上下文感知
4. **可解释性**：聚类原因分析、特征重要性
5. **产品化**：API接口、Web界面、监控告警 