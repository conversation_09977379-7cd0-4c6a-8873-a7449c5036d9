import os
from huggingface_hub import snapshot_download
from dotenv import load_dotenv

# 自动加载根目录下的 .env 文件
load_dotenv(os.path.join(os.path.dirname(os.path.dirname(__file__)), '.env'))

MODEL_NAME = "jinaai/jina-embeddings-v2-base-zh"
LOCAL_DIR = os.path.join(os.path.dirname(__file__), "jina-embeddings-v2-base-zh")
HUGGINGFACE_TOKEN = os.environ.get("HUGGINGFACE_HUB_TOKEN")

if __name__ == "__main__":
    print(f"正在下载模型 {MODEL_NAME} 到 {LOCAL_DIR} ...")
    snapshot_download(
        repo_id=MODEL_NAME,
        local_dir=LOCAL_DIR,
        use_auth_token=HUGGINGFACE_TOKEN
    )
    print("模型下载完成！") 