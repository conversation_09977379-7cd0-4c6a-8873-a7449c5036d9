# 基于BERTopic的用户问题聚类穿刺方案

## 1. 方案背景与目标

随着用户量的增长，用户提出的问题数量激增，内容涵盖中英文、代码、报错、知识问答等多种类型。传统人工归类方式效率低、主观性强，难以满足大规模、自动化分析需求。为此，需引入自动化的文本聚类方案，对用户问题进行高效、可解释的分组，辅助FAQ整理、知识库建设、产品优化等。

本方案目标：
- 利用BERTopic对几万条中英+代码混合的用户问题进行自动聚类与主题发现。
- 输出每个聚类的代表性问题和主题词，便于后续人工校验和知识整理。

## 2. BERTopic简介与原理

BERTopic是一款开源的主题建模与文本聚类工具，核心特点：
- 支持自定义句向量模型（如BERT、bge、text2vec等），适配多语言和代码场景。
- 结合降维（UMAP/t-SNE）与聚类（HDBSCAN/KMeans）算法，自动发现主题。
- 输出每个主题的高频词、代表性文本，具备良好可解释性。
- 支持大规模数据处理和可视化。

原理流程：
1. 文本转句向量（Embedding）
2. 降维（如UMAP）
3. 聚类（如HDBSCAN）
4. 主题词提取（TF-IDF等）

## 3. 适用场景分析
- 用户问题归类与FAQ整理
- 智能客服知识库建设
- 产品反馈聚类与主题发现
- 社区问答、工单、日志等文本分组
- 支持中英文、代码、报错等混合文本

## 4. 具体实施步骤

### 4.1 数据准备
- 收集用户问题文本，建议去重、清洗（如去除无意义内容、统一格式等）。
- 支持中英文、代码、报错等混合内容。

### 4.2 环境与依赖
- 推荐Python 3.8+
- 依赖：bertopic、sentence-transformers、umap-learn、hdbscan、pandas等

```bash
pip install bertopic sentence-transformers umap-learn hdbscan pandas
```

### 4.3 选择合适的Embedding模型
- 推荐多语言模型，如：
  - BAAI/bge-m3
  - paraphrase-multilingual-MiniLM-L12-v2
  - LaBSE
- 可根据实际场景选择，支持自定义本地模型。

### 4.4 聚类流程

```python
from bertopic import BERTopic
from sentence_transformers import SentenceTransformer
import pandas as pd

# 1. 读取数据
df = pd.read_csv('user_questions.csv')  # 假设有一列 'question'
user_questions = df['question'].tolist()

# 2. 加载多语言embedding
embedder = SentenceTransformer('BAAI/bge-m3')

# 3. 建立BERTopic模型
topic_model = BERTopic(embedding_model=embedder, language="multilingual")
topics, probs = topic_model.fit_transform(user_questions)

# 4. 查看主题分布
topic_info = topic_model.get_topic_info()
print(topic_info)

# 5. 导出每个主题下的代表问题
topic_model.get_representative_docs(0)  # 主题0的代表问题

# 6. 可视化（可选）
topic_model.visualize_topics().show()
```

### 4.5 结果分析与人工校验
- 输出每个主题的高频词、代表问题，人工抽查聚类效果。
- 可根据业务需求合并/拆分主题。
- 结果可导出为Excel/CSV，便于后续知识整理。

## 5. 资源与注意事项
- 建议16G以上内存，embedding阶段可用GPU加速。
- 数据量大时，embedding生成最耗时。
- 主题数、聚类参数可根据实际效果调整。
- 如遇中英文+代码混合问题，优先选用多语言embedding。
- 可结合正则/规则先粗分报错类与知识问答类，再细分聚类。

## 6. 参考资料
- [BERTopic官方文档](https://maartengr.github.io/BERTopic/)
- [bge-m3多语言模型](https://huggingface.co/BAAI/bge-m3)
- [BERTopic中文教程](https://zhuanlan.zhihu.com/p/610282011)
- [MTEB聚类评测](https://huggingface.co/datasets/mteb) 