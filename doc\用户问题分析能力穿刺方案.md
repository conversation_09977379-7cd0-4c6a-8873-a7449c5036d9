# 用户问题分析能力穿刺方案（初稿）

## 1. 目标
- 快速验证在本地PC上，利用开源向量模型对中英文+代码混合的用户问题进行意图理解与聚类的可行性。
- 为后续深入开发和团队交流提供技术路线参考。

## 2. 初步方案概述
- 构造一批具有代表性的用户问答数据，涵盖：
  - 中文技术提问
  - 英文技术提问
  - 代码片段、报错信息与自然语言混合
- 使用本地可运行的嵌入模型（如jina-embeddings-v2-base-zh）对问题进行向量化。
- 采用常用聚类算法（如KMeans、DBSCAN）对向量进行聚类，初步观察模型对意图的区分能力。

## 3. 数据构造思路
- 人工编写或收集20-50条典型用户问题，覆盖：
  - 纯中文、纯英文、混合语言
  - 代码+报错+自然语言描述
  - 不同意图类别（如报错排查、用法咨询、优化建议等）
- 每条数据建议包含：问题文本、（可选）标准意图标签

## 4. 模型选择建议
- 首选：jina-embeddings-v2-base-zh（支持中英双语+长上下文，适合本地CPU部署）
- 备选：BAAI/bge-base-zh-v1.5、Alibaba-NLP/gte-large-zh（如需对比）
- 推理工具建议：sentence-transformers库或Ollama

## 5. 实施步骤
1. 数据准备：整理/编写混合场景问答数据
2. 嵌入生成：用本地模型将问题转为向量
3. 聚类分析：用KMeans等算法聚类，人工评估聚类效果
4. 结果交流：整理聚类结果与样例，便于团队讨论

## 6. 后续交流建议
- 方案细化与数据扩充可在此文档持续迭代
- 代码实现建议以Jupyter Notebook或Python脚本形式同步
- 欢迎团队成员补充典型问题样本与意图类别

---

*本方案为快速穿刺验证用，后续可根据实际效果和需求进一步完善。* 