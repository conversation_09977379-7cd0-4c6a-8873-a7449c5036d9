import os
import pandas as pd
from sentence_transformers import SentenceTransformer
from sklearn.cluster import KMeans
import numpy as np
from datetime import datetime

# 1. 数据准备：从CSV读取
DATA_PATH = os.path.join(os.path.dirname(__file__), '..', 'data', 'user_questions_eval.csv')
df = pd.read_csv(DATA_PATH)
user_questions = df['question'].tolist()
true_types = df['type'].tolist()  # 仅用于后续对比，不参与聚类

# 2. 加载本地嵌入模型（jina-embeddings-v2-base-zh）
MODEL_DIR = os.path.join(os.path.dirname(__file__), "..", "model", "jina-embeddings-v2-base-zh")
print("加载本地嵌入模型中……")
model = SentenceTransformer(MODEL_DIR, trust_remote_code=True)

# 3. 生成向量（embedding）
print("正在生成问题向量……")
embeddings = model.encode(user_questions)

# 4. 聚类分析（KMeans）
n_clusters = 5  # 可根据实际情况调整
print(f"正在用KMeans聚类，类别数：{n_clusters}")
kmeans = KMeans(n_clusters=n_clusters, random_state=42)
labels = kmeans.fit_predict(embeddings)

# 5. 准备结果数据
results_df = pd.DataFrame({
    'id': df['id'],
    'question': user_questions,
    'true_type': true_types,
    'cluster_label': labels,
    'cluster_id': [f"cluster_{label}" for label in labels]
})

# 6. 创建输出目录
output_dir = os.path.join(os.path.dirname(__file__), '..', 'results')
os.makedirs(output_dir, exist_ok=True)

# 7. 生成时间戳和方案标识
timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
method_name = "jina_embeddings_kmeans_baseline"

# 8. 保存详细分析结果到CSV
results_file = os.path.join(output_dir, f'analysis_results_{method_name}_{n_clusters}clusters_{timestamp}.csv')
results_df.to_csv(results_file, index=False, encoding='utf-8')
print(f"\n详细分析结果已保存到: {results_file}")

# 9. 生成分析汇总报告
summary_file = os.path.join(output_dir, f'analysis_summary_{method_name}_{n_clusters}clusters_{timestamp}.txt')
with open(summary_file, 'w', encoding='utf-8') as f:
    f.write(f"聚类分析汇总报告\n")
    f.write(f"方案名称: {method_name}\n")
    f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
    f.write(f"嵌入模型: jina-embeddings-v2-base-zh\n")
    f.write(f"聚类算法: KMeans\n")
    f.write(f"聚类数量: {n_clusters}\n")
    f.write(f"样本总数: {len(user_questions)}\n")
    f.write("="*50 + "\n\n")
    
    # 每个聚类的详细信息
    for cluster_id in range(n_clusters):
        cluster_questions = results_df[results_df['cluster_label'] == cluster_id]
        f.write(f"聚类 {cluster_id} (共{len(cluster_questions)}个问题):\n")
        f.write("-" * 30 + "\n")
        
        # 统计该聚类中的人工标签分布
        type_counts = cluster_questions['true_type'].value_counts()
        f.write("人工标签分布:\n")
        for true_type, count in type_counts.items():
            f.write(f"  {true_type}: {count}个\n")
        f.write("\n")
        
        # 列出该聚类的所有问题
        f.write("包含的问题:\n")
        for _, row in cluster_questions.iterrows():
            f.write(f"  {row['id']}. {row['question']} (人工标签: {row['true_type']})\n")
        f.write("\n" + "="*50 + "\n\n")
    
    # 聚类与人工标签的对应分析
    f.write("聚类与人工标签对应分析:\n")
    f.write("-" * 30 + "\n")
    for true_type in set(true_types):
        f.write(f"\n人工标签 '{true_type}' 的分布:\n")
        type_data = results_df[results_df['true_type'] == true_type]
        cluster_dist = type_data['cluster_label'].value_counts().sort_index()
        for cluster_id, count in cluster_dist.items():
            f.write(f"  聚类{cluster_id}: {count}个\n")

print(f"分析汇总报告已保存到: {summary_file}")

# 10. 输出聚类结果，带人工标签
print("\n聚类结果如下：")
for idx, (question, label, true_type) in enumerate(zip(user_questions, labels, true_types)):
    print(f"问题{idx+1}（聚类{label}，人工类型：{true_type}）：{question}")

# 11. 可选：输出每类的样本
print("\n每个类别包含的问题：")
for cluster_id in range(n_clusters):
    cluster_questions = results_df[results_df['cluster_label'] == cluster_id]
    print(f"\n类别{cluster_id} (共{len(cluster_questions)}个问题):")
    for _, row in cluster_questions.iterrows():
        print(f"  - {row['question']}（人工类型：{row['true_type']}）")

print(f"\n所有结果文件已保存到 {output_dir} 目录下")

# 说明：
# - 你可以调整n_clusters，观察聚类与人工标签的对应关系。
# - 可进一步用NMI、ARI等指标量化聚类效果。
# - 运行本脚本前，请确保已安装 sentence-transformers 和 scikit-learn。
#   安装命令：pip install sentence-transformers scikit-learn 
# - 结果文件保存在 results/ 目录下，文件名包含时间戳便于比对不同方案 