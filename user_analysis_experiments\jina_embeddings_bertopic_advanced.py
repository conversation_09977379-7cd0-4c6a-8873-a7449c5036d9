import os
import pandas as pd
from sentence_transformers import SentenceTransformer
from bertopic import BERTopic
from sklearn.feature_extraction.text import CountVectorizer
from umap import UMAP
from hdbscan import HDBSCAN
import numpy as np
from datetime import datetime
import jieba
import warnings
warnings.filterwarnings("ignore")

# 1. 数据准备：从CSV读取
DATA_PATH = os.path.join(os.path.dirname(__file__), '..', 'data', 'user_questions_eval.csv')
df = pd.read_csv(DATA_PATH)
user_questions = df['question'].tolist()
true_types = df['type'].tolist()  # 仅用于后续对比，不参与聚类

# 2. 加载本地嵌入模型（jina-embeddings-v2-base-zh）
MODEL_DIR = os.path.join(os.path.dirname(__file__), "..", "model", "jina-embeddings-v2-base-zh")
print("加载本地嵌入模型中……")
model = SentenceTransformer(MODEL_DIR, trust_remote_code=True)

# 3. 生成向量（embedding）
print("正在生成问题向量……")
embeddings = model.encode(user_questions)

# 4. 设置BERTopic参数
print("正在配置BERTopic模型……")

# 降维组件（UMAP）
umap_model = UMAP(
    n_neighbors=15,
    n_components=5,
    min_dist=0.0,
    metric='cosine',
    random_state=42
)

# 聚类组件（HDBSCAN）
hdbscan_model = HDBSCAN(
    min_cluster_size=3,
    metric='euclidean',
    cluster_selection_method='eom',
    prediction_data=True
)

# 向量化组件（用于主题词提取）
# 支持中文分词的CountVectorizer
def chinese_tokenizer(text):
    """中文分词函数"""
    # 去除特殊字符，保留中英文和数字
    import re
    text = re.sub(r'[^\w\s\u4e00-\u9fff]', ' ', text)
    # 使用jieba分词
    tokens = list(jieba.cut(text.strip()))
    # 过滤长度小于2的词和常见停用词
    stop_words = {'的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好', '自己', '这', '那', '它', '他', '她', '如何', '什么', '怎么', '为什么'}
    tokens = [token for token in tokens if len(token) >= 2 and token not in stop_words]
    return tokens

vectorizer_model = CountVectorizer(
    tokenizer=chinese_tokenizer,
    lowercase=False,
    min_df=1,
    max_df=0.95,
    ngram_range=(1, 2),
    max_features=1000
)

# 5. 创建BERTopic模型
topic_model = BERTopic(
    embedding_model=model,
    umap_model=umap_model,
    hdbscan_model=hdbscan_model,
    vectorizer_model=vectorizer_model,
    language='chinese',
    calculate_probabilities=True,
    verbose=True
)

# 6. 训练模型并获取主题
print("正在进行BERTopic主题建模……")
topics, probs = topic_model.fit_transform(user_questions, embeddings)

# 7. 获取主题信息
topic_info = topic_model.get_topic_info()
print(f"\n发现 {len(topic_info)} 个主题（包括噪声主题-1）")

# 8. 准备结果数据
results_df = pd.DataFrame({
    'id': df['id'],
    'question': user_questions,
    'true_type': true_types,
    'topic_id': topics,
    'topic_probability': probs.max(axis=1) if len(probs.shape) > 1 else probs,
    'topic_label': [f"topic_{topic}" if topic != -1 else "noise" for topic in topics]
})

# 9. 创建输出目录
output_dir = os.path.join(os.path.dirname(__file__), '..', 'results')
os.makedirs(output_dir, exist_ok=True)

# 10. 生成时间戳和方案标识
timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
method_name = "jina_embeddings_bertopic_advanced"

# 11. 保存详细分析结果到CSV
results_file = os.path.join(output_dir, f'analysis_results_{method_name}_{timestamp}.csv')
results_df.to_csv(results_file, index=False, encoding='utf-8')
print(f"\n详细分析结果已保存到: {results_file}")

# 12. 生成分析汇总报告
summary_file = os.path.join(output_dir, f'analysis_summary_{method_name}_{timestamp}.txt')
with open(summary_file, 'w', encoding='utf-8') as f:
    f.write(f"BERTopic主题建模汇总报告\n")
    f.write(f"方案名称: {method_name}\n")
    f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
    f.write(f"嵌入模型: jina-embeddings-v2-base-zh\n")
    f.write(f"主题建模算法: BERTopic (UMAP + HDBSCAN)\n")
    f.write(f"发现主题数量: {len(topic_info)-1}\n")  # 减去噪声主题
    f.write(f"样本总数: {len(user_questions)}\n")
    f.write("="*60 + "\n\n")
    
    # 主题信息概览
    f.write("主题信息概览:\n")
    f.write("-" * 30 + "\n")
    for _, row in topic_info.iterrows():
        topic_id = row['Topic']
        if topic_id == -1:
            f.write(f"噪声主题 (Topic -1): {row['Count']}个样本\n")
        else:
            topic_words = topic_model.get_topic(topic_id)
            top_words = [word for word, score in topic_words[:5]]
            f.write(f"主题 {topic_id}: {row['Count']}个样本, 关键词: {', '.join(top_words)}\n")
    f.write("\n" + "="*60 + "\n\n")
    
    # 每个主题的详细信息
    for topic_id in range(len(topic_info)-1):  # 跳过噪声主题
        if topic_id in topic_model.topic_labels_.keys():
            topic_questions = results_df[results_df['topic_id'] == topic_id]
            if len(topic_questions) == 0:
                continue
                
            f.write(f"主题 {topic_id} 详细分析 (共{len(topic_questions)}个问题):\n")
            f.write("-" * 40 + "\n")
            
            # 主题关键词
            topic_words = topic_model.get_topic(topic_id)
            f.write("主题关键词 (词-权重):\n")
            for word, score in topic_words[:10]:
                f.write(f"  {word}: {score:.4f}\n")
            f.write("\n")
            
            # 统计该主题中的人工标签分布
            type_counts = topic_questions['true_type'].value_counts()
            f.write("人工标签分布:\n")
            for true_type, count in type_counts.items():
                percentage = count / len(topic_questions) * 100
                f.write(f"  {true_type}: {count}个 ({percentage:.1f}%)\n")
            f.write("\n")
            
            # 代表性问题（置信度最高的前5个）
            representative_questions = topic_questions.nlargest(5, 'topic_probability')
            f.write("代表性问题 (按置信度排序):\n")
            for _, row in representative_questions.iterrows():
                f.write(f"  {row['id']}. {row['question']} (置信度: {row['topic_probability']:.3f}, 人工标签: {row['true_type']})\n")
            f.write("\n")
            
            # 所有问题列表
            f.write("包含的所有问题:\n")
            for _, row in topic_questions.iterrows():
                f.write(f"  {row['id']}. {row['question']} (置信度: {row['topic_probability']:.3f}, 人工标签: {row['true_type']})\n")
            f.write("\n" + "="*60 + "\n\n")
    
    # 噪声主题分析
    noise_questions = results_df[results_df['topic_id'] == -1]
    if len(noise_questions) > 0:
        f.write(f"噪声主题分析 (共{len(noise_questions)}个问题):\n")
        f.write("-" * 30 + "\n")
        f.write("这些问题未能归入任何明确主题，可能是:\n")
        f.write("1. 独特的单例问题\n")
        f.write("2. 表达方式特殊的问题\n")
        f.write("3. 数据质量问题\n\n")
        
        type_counts = noise_questions['true_type'].value_counts()
        f.write("人工标签分布:\n")
        for true_type, count in type_counts.items():
            f.write(f"  {true_type}: {count}个\n")
        f.write("\n")
        
        f.write("噪声问题列表:\n")
        for _, row in noise_questions.iterrows():
            f.write(f"  {row['id']}. {row['question']} (人工标签: {row['true_type']})\n")
        f.write("\n" + "="*60 + "\n\n")
    
    # 主题与人工标签的对应分析
    f.write("主题与人工标签对应分析:\n")
    f.write("-" * 30 + "\n")
    for true_type in set(true_types):
        f.write(f"\n人工标签 '{true_type}' 的主题分布:\n")
        type_data = results_df[results_df['true_type'] == true_type]
        topic_dist = type_data['topic_id'].value_counts().sort_index()
        for topic_id, count in topic_dist.items():
            percentage = count / len(type_data) * 100
            topic_name = f"主题{topic_id}" if topic_id != -1 else "噪声主题"
            f.write(f"  {topic_name}: {count}个 ({percentage:.1f}%)\n")

print(f"BERTopic分析汇总报告已保存到: {summary_file}")

# 13. 输出主题结果
print("\nBERTopic主题建模结果:")
print("="*50)
for topic_id in range(len(topic_info)-1):
    if topic_id in topic_model.topic_labels_.keys():
        topic_questions = results_df[results_df['topic_id'] == topic_id]
        if len(topic_questions) == 0:
            continue
        topic_words = topic_model.get_topic(topic_id)
        top_words = [word for word, score in topic_words[:5]]
        print(f"\n主题 {topic_id} (共{len(topic_questions)}个问题):")
        print(f"关键词: {', '.join(top_words)}")
        print("问题示例:")
        for _, row in topic_questions.head(3).iterrows():
            print(f"  - {row['question']}")

# 14. 保存主题模型（可选）
model_save_path = os.path.join(output_dir, f'bertopic_model_{timestamp}')
try:
    topic_model.save(model_save_path)
    print(f"\nBERTopic模型已保存到: {model_save_path}")
except Exception as e:
    print(f"\n模型保存失败: {e}")

print(f"\n所有结果文件已保存到 {output_dir} 目录下")

# 说明：
# - BERTopic会自动确定主题数量，相比KMeans更灵活
# - 主题-1表示噪声/离群点，这些样本不属于任何明确主题
# - 可以通过调整HDBSCAN的min_cluster_size参数来控制主题的细粒度
# - 运行本脚本前，请确保已安装：pip install bertopic umap-learn hdbscan jieba
# - 结果包含主题关键词、代表性文档和置信度，可解释性更强 