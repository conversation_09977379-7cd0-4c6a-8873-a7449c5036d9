# 面向聚类的用户问题预处理方案

本方案专门针对AI编程助手的用户问题聚类分析需求，重点解决中英文混合、代码片段、报错信息等复杂输入的预处理问题。方案聚焦于为后续BERTopic聚类提供最优输入，同时保留有利于聚类的语义特征。

---

## 一、输入场景分析（要处理的数据形式及样例）

### 1. 代码片段场景
**特征**：包含明显的代码块、函数、变量、缩进、标记符号等。

**数据样例**：
```
输入1: "如何实现用户登录功能？```python\ndef login(username, password):\n    if verify_user(username, password):\n        return True\n    return False\n```"

输入2: "我的代码报错了 <code>TypeError: 'NoneType' object is not subscriptable</code> 怎么解决？"

输入3: "    def process_data(data):
        if data is None:
            return []
        return data.split(',')
这段代码有什么问题？"
```

### 2. 纯自然语言场景
**特征**：用户直接用中文/英文描述问题，无代码。

**数据样例**：
```
输入1: "如何修改用户密码？"
输入2: "订单支付失败怎么办？"
输入3: "How to reset password?"
```

### 3. 混合输入场景（自然语言+代码）
**特征**：自然语言描述+代码片段混杂。

**数据样例**：
```
输入1: "我想实现一个登录功能，但是这个代码```python\nif user.login():\n    print('success')\n```总是返回False，请问是什么原因？"

输入2: "用户注册时需要验证邮箱，我写了这段代码<code>validate_email(email)</code>，但是不知道怎么集成到注册流程中？"
```

### 4. 特殊符号、表情、标点场景
**特征**：包含emoji、特殊符号、重复标点等。

**数据样例**：
```
输入1: "登录失败了😭😭😭怎么办？？？"
输入2: "支付功能好用！！！！👍👍👍"
输入3: "测试测试。。。。。。"
```

### 5. 英文、拼音混杂场景
**特征**：中英文、拼音混合输入。

**数据样例**：
```
输入1: "zenme xiugai password？"
输入2: "如何reset用户密码？"
输入3: "login功能怎么实现的？"
```

### 6. 重复内容、无意义内容场景
**特征**：测试数据、无实际意义内容。

**数据样例**：
```
输入1: "哈哈哈哈哈哈哈哈"
输入2: "测试测试测试测试"
输入3: "1111111111"
输入4: "aaaaaaaaaa"
```

### 7. HTML标签、转义字符场景
**特征**：包含HTML标签、网页转义字符等。

**数据样例**：
```
输入1: "如何实现<strong>用户登录</strong>功能？"
输入2: "支付失败&nbsp;&nbsp;怎么办？"
输入3: "<p>订单状态如何查询？</p>"
```

### 8. 空白、换行、制表符场景
**特征**：多余空格、换行、制表符等。

**数据样例**：
```
输入1: "   如何修改用户密码？   "
输入2: "登录功能\n\n\n怎么实现？"
输入3: "支付\t\t\t失败怎么办？"
```

---

## 二、期望输出目标

### 统一输出格式
**目标**：将所有复杂输入统一处理成干净的、适合主题建模的标准化文本。

**输出特征**：
- 纯中文自然语言描述（保留必要的英文专业术语）
- 去除代码片段、特殊符号、HTML标签等噪声
- 保留核心用户意图和问题描述
- 统一空白符和标点符号
- 过滤无意义内容

**输出样例**：
```
场景1输入: "如何实现用户登录功能？```python\ndef login(username, password):\n    if verify_user(username, password):\n        return True\n    return False\n```"
期望输出: "如何实现用户登录功能"

场景4输入: "登录失败了😭😭😭怎么办？？？"
期望输出: "登录失败了怎么办"

场景7输入: "如何实现<strong>用户登录</strong>功能？"
期望输出: "如何实现用户登录功能"
```

---

## 三、具体处理过程（输入→输出转换规则）

### 处理流程总览
```
原始输入 → 字符编码统一 → HTML/转义处理 → 代码片段处理 → 特殊符号处理 → 标点归一化 → 空白处理 → 无意义内容过滤 → 最终输出
```

### 各场景具体转换规则

#### 场景1：代码片段处理
**转换规则**：
- 识别并移除代码块：`re.sub(r'```[\s\S]*?```', '', text)`
- 移除HTML代码标签：`re.sub(r'<code>[\s\S]*?</code>', '', text)`
- 移除缩进代码：`re.sub(r'(?m)^\s{4,}.*$', '', text)`
- 可选：用占位符替换：`re.sub(r'```[\s\S]*?```', '[代码片段]', text)`

**转换示例**：
```python
# 输入
"如何实现用户登录功能？```python\ndef login():\n    pass\n```"

# 处理过程
step1 = re.sub(r'```[\s\S]*?```', '', text)  # "如何实现用户登录功能？"
step2 = step1.strip()  # "如何实现用户登录功能？"

# 输出
"如何实现用户登录功能"
```

#### 场景2：纯自然语言处理
**转换规则**：
- 保留主体内容
- 基础清理：去除多余空白、标点归一化

**转换示例**：
```python
# 输入
"如何修改用户密码？"

# 处理过程
step1 = text.strip()  # 去除首尾空白
step2 = re.sub(r'\s+', ' ', step1)  # 归一化空白

# 输出
"如何修改用户密码"
```

#### 场景3：混合输入处理
**转换规则**：
- 先执行代码片段处理规则
- 再执行自然语言处理规则

#### 场景4：特殊符号、表情处理
**转换规则**：
- 移除emoji：`re.sub(r'[\U00010000-\U0010ffff]', '', text)`
- 归一化重复标点：`re.sub(r'[!！?？]{2,}', lambda m: m.group(0)[0], text)`

**转换示例**：
```python
# 输入
"登录失败了😭😭😭怎么办？？？"

# 处理过程
step1 = re.sub(r'[\U00010000-\U0010ffff]', '', text)  # "登录失败了怎么办？？？"
step2 = re.sub(r'[?？]{2,}', '？', step1)  # "登录失败了怎么办？"

# 输出
"登录失败了怎么办"
```

#### 场景5：英文、拼音混杂处理
**转换规则**：
- 保留英文专业术语（如login、password等）
- 移除纯拼音或根据词典转换

#### 场景6：无意义内容过滤
**转换规则**：
- 检测重复字符：`re.sub(r'(.)\1{3,}', '', text)`
- 设置最小长度阈值
- 使用停用词表过滤

**转换示例**：
```python
# 输入
"哈哈哈哈哈哈哈哈"

# 处理过程
step1 = re.sub(r'(.)\1{3,}', '', text)  # ""
if len(step1.strip()) < 2:  # 长度检查
    return None  # 标记为无效

# 输出
None (过滤掉)
```

#### 场景7：HTML标签处理
**转换规则**：
- 移除HTML标签：`re.sub(r'<[^>]+>', '', text)`
- 转义字符还原：`html.unescape(text)`

#### 场景8：空白处理
**转换规则**：
- 去除首尾空白：`text.strip()`
- 归一化内部空白：`re.sub(r'\s+', ' ', text)`

---

## 四、完整处理代码实现

```python
import re
import html

def preprocess_user_question(text):
    """
    用户问题预处理主函数
    输入：原始用户问题文本
    输出：清洗后的标准化文本，如果无效则返回None
    """
    if not text or not isinstance(text, str):
        return None
    
    # 1. 字符编码统一
    text = text.encode('utf-8').decode('utf-8')
    
    # 2. HTML标签和转义字符处理
    text = re.sub(r'<[^>]+>', '', text)  # 移除HTML标签
    text = html.unescape(text)  # 转义字符还原
    
    # 3. 代码片段处理
    text = re.sub(r'```[\s\S]*?```', '', text)  # 移除代码块
    text = re.sub(r'<code>[\s\S]*?</code>', '', text)  # 移除code标签
    text = re.sub(r'(?m)^\s{4,}.*$', '', text)  # 移除缩进代码
    
    # 4. 特殊符号和emoji处理
    text = re.sub(r'[\U00010000-\U0010ffff]', '', text)  # 移除emoji
    text = re.sub(r'[!！]{2,}', '！', text)  # 归一化感叹号
    text = re.sub(r'[?？]{2,}', '？', text)  # 归一化问号
    text = re.sub(r'[.。]{3,}', '。', text)  # 归一化省略号
    
    # 5. 重复字符处理
    text = re.sub(r'(.)\1{3,}', r'\1', text)  # 移除连续重复字符
    
    # 6. 空白和换行处理
    text = re.sub(r'\s+', ' ', text)  # 归一化空白
    text = text.strip()  # 去除首尾空白
    
    # 7. 无意义内容过滤
    if len(text) < 2:  # 长度过短
        return None
    if re.match(r'^[a-zA-Z0-9\s]*$', text):  # 纯英文数字
        return None
    if re.match(r'^[^\u4e00-\u9fff]*$', text):  # 无中文
        return None
    
    # 8. 最终清理
    text = re.sub(r'^[^\u4e00-\u9fff]*', '', text)  # 去除开头非中文
    text = re.sub(r'[^\u4e00-\u9fff]*$', '', text)  # 去除结尾非中文
    
    return text if text else None

# 批量处理函数
def batch_preprocess(questions):
    """批量处理用户问题"""
    results = []
    for i, question in enumerate(questions):
        processed = preprocess_user_question(question)
        results.append({
            'id': i,
            'original': question,
            'processed': processed,
            'is_valid': processed is not None
        })
    return results
```

---

## 五、处理效果验证

### 测试用例
```python
test_cases = [
    "如何实现用户登录功能？```python\ndef login():\n    pass\n```",
    "登录失败了😭😭😭怎么办？？？",
    "如何实现<strong>用户登录</strong>功能？",
    "   如何修改用户密码？   ",
    "哈哈哈哈哈哈哈哈",
    "zenme xiugai password？",
]

for case in test_cases:
    result = preprocess_user_question(case)
    print(f"输入: {case}")
    print(f"输出: {result}")
    print("-" * 50)
```

---

## 六、应用建议

### 1. 集成到现有流程
- 在BERTopic主题建模之前，先对原始数据进行预处理
- 保留原始文本和处理后文本的映射关系，便于结果溯源

### 2. 参数调优
- 根据实际数据特点，调整正则表达式和过滤规则
- 设置合适的文本长度阈值
- 根据业务需求调整英文专业术语的保留策略

### 3. 质量监控
- 定期检查预处理效果，统计过滤掉的数据比例
- 人工抽样验证处理结果的准确性
- 根据新出现的数据模式，及时更新处理规则

如需针对具体业务场景进一步细化规则，可随时补充！ 